7� -�       H]�6�o��D[.��6    p/H]�6�o�ދG>=�SQLite format 3   @      p/  ��  �   )                                                  .n�
   
v �L
v�
�nDQ�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�(33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)    p/H]�6�o���q�"şASQLite format 3   @      p/  ��  �   *                                                  .n�
   � �L
v�
�nDQ�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o�5�ˠ���SQLite format 3   @      p/  ��  �   +                                                  .n�
{ � �L
v��4DQ�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' �?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions ��       �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o�+��n�ݷ
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                ��    H]�6�o�a,�&�'  ��  U  �  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  �� p/H]�6�o�ˮO�.�Gg
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     H]�6�o����1R�SQLite format 3   @      p/  ��  �   ,                                                  .n�
{ � �L
v��4��Q�                                                                                                                                                                                                                 �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            OR�(
33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT,
                PRIMARY KEY (table_name, column_name)
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions ��       �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)  ��    H]�6�o�:����ߘ�  ��  S  �  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o��L���
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                �� p/H]�6�o��%�?p�
                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                     H]�6�o�z<�!�zQSQLite format 3   @      p/  ��  �   -                                                  .n�
{ 	Q �L
v��4��Q�                                                                                                                                                                                                                 �*))�tablebusiness_rulesbusiness_rules ��CREATE TABLE business_rules (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                table_name TEXT NOT NULL,
                rule_category TEXT NOT NULL,
                rule_description TEXT NOT NULL,
                sql_example TEXT,
                importance_level TEXT DEFAULT 'HIGH',
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )�(
33�stablecolumn_descriptionscolumn_descriptions ��CREATE TABLE column_descriptions (
                table_name TEXT NOT NULL,
                column_name TEXT NOT NULL,
                chinese_name TEXT,
                description TEXT,
                data_type TEXT,
                business_rules TEXT,
                ai_understanding_points TEXT,
                PRIMARY KEY (table_name, column_name)
            )GY3 indexsqlite_autoindex_column_descriptions_1column_descriptions �Ă?11�)tabletable_descriptionstable_descriptionsCREATE TABLE table_descriptions (
                table_name TEXT PRIMARY KEY,
                description TEXT NOT NULL,
                business_purpose TEXT,
                data_scale TEXT,
                created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
            )EW1 indexsqlite_autoindex_table_descriptions_1table_descriptions ��       �p
KK�[viewfinancial_data_columns_metadatafinancial_data_columns_metadataCREATE VIEW financial_data_columns_metadata AS
            SELECT 
                column_name,
                chinese_name,
                description,
                data_type,
                business_rules,
                ai_understanding_points
            FROM column_descriptions
            WHERE table_name = 'financial_data'
            ORDER BY 
                CASE 
                    WHEN column_name IN ('year', 'month') THEN 1
                    WHEN column_name LIKE 'accounting_%' THEN 2
                    WHEN column_name LIKE 'account_%' THEN 3
                    WHEN column_name LIKE '%amount%' OR column_name LIKE '%cumulative%' OR column_name = 'balance' THEN 4
                    WHEN column_name LIKE 'project_%' THEN 5
                    WHEN column_name LIKE 'bank_%' OR column_name LIKE 'financial_institution_%' THEN 6
                    ELSE 7
                END,
                column_name�S	EE�-viewfinancial_data_with_metadatafinancial_data_with_metadataCREATE VIEW financial_data_with_metadata AS
            SELECT 
                'financial_data' as table_name,
                td.description as table_description,
                td.business_purpose,
                td.data_scale,
                (SELECT COUNT(*) FROM financial_data) as current_record_count
            FROM table_descriptions td
            WHERE td.table_name = 'financial_data'R++Ytablesqlite_sequencesqlite_sequence ��CREATE TABLE sqlite_sequence(name,seq)�]))�qtablefinancial_datafinancial_data �CREATE TABLE "financial_data" ("year" INTEGER, "month" INTEGER, "accounting_organization" INTEGER, "accounting_unit_name" TEXT, "account_code" INTEGER, "account_full_name" TEXT, "account_name" TEXT, "opening_debit_amount" REAL, "opening_credit_amount" INTEGER, "account_direction" TEXT, "project_id" TEXT, "project_code" TEXT, "project_name" TEXT, "market_nature_id" TEXT, "tax_rate_id" TEXT, "tax_rate_name" TEXT, "business_format_id" TEXT, "financial_product_id" TEXT, "long_term_deferred_project_id" TEXT, "property_unit_id" TEXT, "cash_flow_project_id" TEXT, "municipal_enterprise_unit_id" TEXT, "bank_account_id" TEXT, "financial_institution_id" INTEGER, "bank_routing_number" INTEGER, "bank_name" TEXT, "debit_amount" REAL, "debit_cumulative" REAL, "credit_amount" REAL, "credit_cumulative" REAL, "balance" TEXT)       H]�6�o���	b�3̑
   
� 
�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        �{	)���93financial_data财务辅助科目余额表，包含了企业财务核算的详细信息。
        该表是企业财务管理系统的核心数据表，涵盖了时间维度、组织架构、会计科目、项目管理、银行信息和金额数据等多个方面。用于企业财务核算、报表编制、财务分析和决策支持。
        支持按时间、组织、项目、科目等多维度进行财务数据分析。
        为资产负债表、利润表、现金流量表等财务报表提供基础数据。723,333 行记录，31 个字段，包含整数、实数、文本等多种数据类型2025-07-28 04:19:39  ��    H]�6�o��y� g  ��  P  ��  �  �  �  �  �  �  �  �  �  �  �
  �  �  �
  �	  �  �  �  �  �  �  �  �  �   ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �   �  �  �  �  �  �  �  �  �	  �
  �  �  �
  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �  �   �!  �"  �#  �$  �%  �&  �'  �(  �)  �*  �+  �,  �-  �.  �/  �0  �1  �2  �3  �4  �5  �6  �7  �8  �9  �:  �;  �<  �=  �>  �?  �@  �A  �B  �C  �D  �E  �F  �G  �H  �I  �J  �K  �L  �M  �N  �O  �P  �Q  �R  �S  �T  �U  �V  �W  �X  �Y  �Z  �[  �\  �]  �^  �_  �`  �a  �b  �c  �d  �e  �f  �g  �h  �i  �j  �k  �l  �n  �o  �p  �q  �r  �s  �t  �u  �v  �w  �x  �y  �z  �{  �|  �}  �~  �  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  �m  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��  ��   行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部�-4480.00000000�B��!�;%%


UU+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部p�-28879.00000000�D��!�;%%


UU#�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部@Iٙ����51.70000000�I��!�;%%


UU-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       a4be09b9-c44f-4194-a848-2d1aa0d1911c 49777ac1-2845-8af5-1789-54cb4a72c850 G�
0� G�
0�招商银行股份有限公司深圳分行票据业务部A������-340922.95000000�,��!�;%%


UUO-�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       f5e52502-a987-41cd-9628-9e72ce7e508f 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部T�-152735.00000000�/��!�;%%


UUO/�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       79035880-87f3-4a3b-ae13-6ab820525793 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部 XX�-1325136.00000000�0��!�;%%


UUO)�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       5ec52f35-fa80-4795-9f3d-df0dd5006c6a 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@�Y�\)-1789.49000000�8��!�;%%


UUO+�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       3465b495-bfa8-41bd-99fe-49e8efb61e26 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部A(۞z�H�ATz�G�814577.70000000�-��!�;%%


UUO%�T365安居（深圳）城市运营科技服务有限公司安居鸿栖台运营服务中心�银行存款银行存款借       7604f734-3f8a-4767-a39e-08183d22cb35 49777ac1-2845-8af5-1789-54cb4a72c850 G�
. G�
.招商银行深圳分行营业部@nz�G�240.19000000  ��    H]�6�o�Ẍ́��A��
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    )	financial_data  ��    H]�6�o��[rԔN�   �   ��
f�	<
l	��@����n�P�J�2 l                                                    �C)G5Uaafinancial_datalong_term_deferred_project_id长期待摊项目ID长期待摊费用项目的标识符TEXT用于长期待摊费用的核算和分摊用于长期待摊费用的核算和分摊�)5)===financial_datafinancial_product_id金融产品ID金融产品的标识符TEXT关联金融产品信息关联金融产品信息�)1=OOfinancial_databusiness_format_id业态ID业务形态的标识符TEXT用于按业务形态分类统计用于按业务形态分类统计�	)'%77efinancial_datatax_rate_name税率名称税率的名称描述TEXT税率的具体名称税率的具体名称，如"13%增值税"等x)#11[financial_datatax_rate_id税率ID税率的标识符TEXT关联税率信息关联税率信息，用于税务分析�)-)=OOfinancial_datamarket_nature_id市场性质ID市场性质的标识符TEXT用于按市场性质分类分析用于按市场性质分类分析�)%%77afinancial_dataproject_name项目名称项目的名称描述TEXT项目的具体名称项目的具体名称，用于报表展示� )%%+7afinancial_dataproject_code项目编号项目的编码TEXT项目的业务编号项目的业务编号，便于项目管理�)!=Oafinancial_dataproject_id项目ID项目的唯一标识符TEXT用于项目维度的财务分析用于项目维度的财务分析和核算�)7ggfinancial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息�8)/%aOyfinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析�7
)-%aOyfinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析�,	)'%C7�financial_datacredit_amount贷方金额当期发生的贷方金额REAL当期贷方发生额当期贷方发生额，反映当期业务活动对科目的贷方影响�+	)%%C7�financial_datadebit_amount借方金额当期发生的借方金额REAL当期借方发生额当期借方发生额，反映当期业务活动对科目的借方影响�C
	)71UC�financial_dataopening_credit_amount期初贷方金额会计期间开始时的贷方余额INTEGER期初余额的贷方部分期初余额的贷方部分，与期初借方金额配合使用�5	)51UCyfinancial_dataopening_debit_amount期初借方金额会计期间开始时的借方余额REAL期初余额的借方部分期初余额的借方部分，用于计算期末余额�M	)/%O;�Gfinancial_dataaccount_direction科目方向会计科目的借贷方向属性TEXT取值: "借" 或 "贷"决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方�J	)%%Cw�financial_dataaccount_name科目名称会计科目的简化名称TEXT通常与account_full_name相同或为其简化版本科目的简称，通常与account_full_name相同或为其简化版本�)/%C7sfinancial_dataaccount_full_name科目全称会计科目的完整名称TEXT科目的详细描述科目的详细描述，用于财务报表和分析�=	)%%C[�financial_dataaccount_code科目编号会计科目的数字编码INTEGER遵循会计准则的科目编码规则会计科目的唯一标识，遵循会计准则的科目编码规则�6	)51CC�financial_dataaccounting_unit_name核算单位名称核算单位的完整名称TEXT核算主体的具体名称核算主体的具体名称，用于报表展示和数据筛选�2);%COyfinancial_dataaccounting_organization核算组织核算组织的代码标识INTEGER用于区分不同的核算主体用于区分不同的核算主体，进行分组统计�	)O1�financial_datamonth月财务数据所属的会计月份INTEGER取值范围: 1-12与year字段配合，精确定位财务数据的时间点�)O=yfinancial_datayear年财务数据所属的会计年度INTEGER用于时间序列分析用于时间序列分析，按年度统计财务�  ��  ��    H]�6�o�s�����
   � ]�8�}
���
pa�=���
{�@��
�
�
��
\
;�                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                       )financial_databank_name&)3financial_databank_routing_number+)=financial_datafinancial_institution_id")+financial_databank_account_id/)Efinancial_datamunicipal_enterprise_unit_id')5financial_datacash_flow_project_id#)-financial_dataproperty_unit_id0)Gfinancial_datalong_term_deferred_project_id')5financial_datafinancial_product_id%)1financial_databusiness_format_id )'financial_datatax_rate_name)#financial_datatax_rate_id#)-financial_datamarket_nature_id)%financial_dataproject_name)%financial_dataproject_code)!financial_dataproject_id)financial_databalance$)/financial_datacredit_cumulative#)-financial_datadebit_cumulative
 )'financial_datacredit_amount)%financial_datadebit_amount()7financial_dataopening_credit_amount
')5financial_dataopening_debit_amount	$)/financial_dataaccount_direction)%financial_dataaccount_name$)/financial_dataaccount_full_name)%financial_dataaccount_code')5financial_dataaccounting_unit_name*);financial_dataaccounting_organization)financial_datamonth)	financial_datayear  ��    H]�6�o�ۅZ:�%e
    l j�
f�	<
l	��@����n�P�J�2 l                                                    �C)G5Uaafinancial_datalong_term_deferred_project_id长期待摊项目ID长期待摊费用项目的标识符TEXT用于长期待摊费用的核算和分摊用于长期待摊费用的核算和分摊�)5)===financial_datafinancial_product_id金融产品ID金融产品的标识符TEXT关联金融产品信息关联金融产品信息�)1=OOfinancial_databusiness_format_id业态ID业务形态的标识符TEXT用于按业务形态分类统计用于按业务形态分类统计�	)'%77efinancial_datatax_rate_name税率名称税率的名称描述TEXT税率的具体名称税率的具体名称，如"13%增值税"等x)#11[financial_datatax_rate_id税率ID税率的标识符TEXT关联税率信息关联税率信息，用于税务分析�)-)=OOfinancial_datamarket_nature_id市场性质ID市场性质的标识符TEXT用于按市场性质分类分析用于按市场性质分类分析�)%%77afinancial_dataproject_name项目名称项目的名称描述TEXT项目的具体名称项目的具体名称，用于报表展示� )%%+7afinancial_dataproject_code项目编号项目的编码TEXT项目的业务编号项目的业务编号，便于项目管理�)!=Oafinancial_dataproject_id项目ID项目的唯一标识符TEXT用于项目维度的财务分析用于项目维度的财务分析和核算�)7ggfinancial_databalance余额科目的期末余额TEXT期末余额，可能包含借贷方向信息期末余额，可能包含借贷方向信息�8)/%aOyfinancial_datacredit_cumulative贷方累计从年初到当期的贷方累计发生额REAL年初至今的贷方累计金额年初至今的贷方累计金额，用于年度分析�7
)-%aOyfinancial_datadebit_cumulative借方累计从年初到当期的借方累计发生额REAL年初至今的借方累计金额年初至今的借方累计金额，用于年度分析�,	)'%C7�financial_datacredit_amount贷方金额当期发生的贷方金额REAL当期贷方发生额当期贷方发生额，反映当期业务活动对科目的贷方影响�+	)%%C7�financial_datadebit_amount借方金额当期发生的借方金额REAL当期借方发生额当期借方发生额，反映当期业务活动对科目的借方影响�C
	)71UC�financial_dataopening_credit_amount期初贷方金额会计期间开始时的贷方余额INTEGER期初余额的贷方部分期初余额的贷方部分，与期初借方金额配合使用�5	)51UCyfinancial_dataopening_debit_amount期初借方金额会计期间开始时的借方余额REAL期初余额的借方部分期初余额的借方部分，用于计算期末余额�M	)/%O;�Gfinancial_dataaccount_direction科目方向会计科目的借贷方向属性TEXT取值: "借" 或 "贷"决定科目的正常余额方向，借方科目增加记借方，贷方科目增加记贷方�J	)%%Cw�financial_dataaccount_name科目名称会计科目的简化名称TEXT通常与account_full_name相同或为其简化版本科目的简称，通常与account_full_name相同或为其简化版本�)/%C7sfinancial_dataaccount_full_name科目全称会计科目的完整名称TEXT科目的详细描述科目的详细描述，用于财务报表和分析�=	)%%C[�financial_dataaccount_code科目编号会计科目的数字编码INTEGER遵循会计准则的科目编码规则会计科目的唯一标识，遵循会计准则的科目编码规则�6	)51CC�financial_dataaccounting_unit_name核算单位名称核算单位的完整名称TEXT核算主体的具体名称核算主体的具体名称，用于报表展示和数据筛选�2);%COyfinancial_dataaccounting_organization核算组织核算组织的代码标识INTEGER用于区分不同的核算主体用于区分不同的核算主体，进行分组统计�	)O1�financial_datamonth月财务数据所属的会计月份INTEGER取值范围: 1-12与year字段配合，精确定位财务数据的时间点�)O=yfinancial_datayear年财务数据所属的会计年度INTEGER用于时间序列分析用于时间序列分析，按年度统计财务数据  ��    H]�6�o��2{(��]�
   ~ 9
7z~                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                            �y
 )1�Y�13financial_data科目编号识别1xxx=资产类，2xxx=负债类，3xxx=所有者权益类，60xx=收入类，64xx/66xx=成本费用类SELECT account_code, account_name FROM financial_data WHERE account_code LIKE "1%"HIGH2025-07-28 04:19:39�:	 )1�!m3financial_data数据类型转换balance字段为TEXT类型，需要使用CAST(balance AS REAL)进行转换SELECT CAST(balance AS REAL) FROM financial_dataHIGH2025-07-28 04:19:39�
 )O��W3financial_data科目分类与金额字段对应成本费用类科目必须使用debit_amount或debit_cumulative字段SELECT SUM(debit_amount) FROM financial_data WHERE account_code LIKE "64%" OR account_code LIKE "66%"CRITICAL2025-07-28 04:19:39�a
 )O��#3financial_data科目分类与金额字段对应收入类科目必须使用credit_amount或credit_cumulative字段SELECT SUM(credit_amount) FROM financial_data WHERE account_code LIKE "60%"CRITICAL2025-07-28 04:19:39�`
 )O��13financial_data科目分类与金额字段对应资产负债类科目必须使用balance字段进行汇总SELECT SUM(CAST(balance AS REAL)) FROM financial_data WHERE account_code LIKE "1%"CRITICAL2025-07-28 04:19:39  ��    H]�6�o�r���	�m#
   � �                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                  )business_rules  � p/H]�6�o���
?�D�
   � A�
�
@��                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                    �)%+7kfinancial_databank_name银行名称银行的名称TEXT具体的银行名称具体的银行名称，如"中国工商银行"�
)37IIfinancial_databank_routing_number联行号银行的联行号码INTEGER银行间清算的标识号码银行间清算的标识号码�)=)=CCfinancial_datafinancial_institution_id金融机构ID金融机构的标识符INTEGER标识具体的金融机构标识具体的金融机构�)+)=OOfinancial_databank_account_id银行账号ID银行账户的标识符TEXT关联具体的银行账户信息关联具体的银行账户信息�6)E5UUUfinancial_datamunicipal_enterprise_unit_id市属国企单位ID市属国有企业单位的标识符TEXT特定于国有企业的分类标识特定于国有企业的分类标识�1)55O[[financial_datacash_flow_project_id现金流量项目ID现金流量表项目的标识符TEXT用于现金流量表的编制和分析用于现金流量表的编制和分析�<)-)aggfinancial_dataproperty_unit_id楼盘房号ID房地产项目中具体房号的标识符TEXT房地产业务中的具体房产单位标识房地产业务中的具体房产单位标识